# So sánh 2 phương án xử lý API courses list

## Phương án 1: Override action list (ĐÃ SỬA LỖI)

### Ưu điểm:
- **Kiể<PERSON> soát hoàn toàn**: <PERSON><PERSON> thể tùy chỉnh toàn bộ logic của action list
- **Xử lý lỗi tốt hơn**: <PERSON><PERSON> thể handle các edge cases một cách chi tiết
- **Performance tối ưu**: <PERSON><PERSON> thể optimize query và logic theo nhu cầu cụ thể
- **Flexibility cao**: <PERSON><PERSON> thể thay đổi cấu trúc response nếu cần

### Nhược điểm:
- **Phức tạp hơn**: Phải tự implement pagination và các logic cơ bản
- **Maintenance**: Cần maintain code nhiều hơn
- **Risk cao**: Nếu có lỗi có thể ảnh hưởng đến toàn bộ list functionality

### Code mẫu (đã fix lỗi):
```javascript
list: {
  rest: 'GET /',
  auth: 'required',
  async handler(ctx) {
    // Xử lý query an toàn
    let query = {};
    if (ctx.params.query) {
      if (typeof ctx.params.query === 'string') {
        try {
          query = JSON.parse(ctx.params.query);
        } catch (error) {
          this.logger.warn('Invalid JSON in query parameter:', ctx.params.query);
          query = {};
        }
      } else if (typeof ctx.params.query === 'object') {
        query = ctx.params.query;
      }
    }
    
    // Gọi adapter.list và enrich data
    const result = await this.adapter.list(listParams);
    return await this.enrichCoursesData(ctx, result);
  }
}
```

## Phương án 2: Hook after cho action list (ĐANG SỬ DỤNG)

### Ưu điểm:
- **Đơn giản**: Sử dụng list action mặc định từ moleculer-db
- **Ít risk**: Không thay đổi core functionality
- **Dễ maintain**: Code ít hơn, logic rõ ràng
- **Tương thích tốt**: Hoạt động với tất cả features có sẵn của baseService

### Nhược điểm:
- **Ít control**: Không thể thay đổi cấu trúc query hoặc response format
- **Performance**: Có thể chậm hơn vì phải process data sau khi đã có kết quả

### Code hiện tại:
```javascript
hooks: {
  after: {
    list: async function (ctx, res) {
      const user = ctx.meta.user;
      
      if (!user || !res || !res.rows || !Array.isArray(res.rows)) {
        return res;
      }

      try {
        const enrichedRows = await Promise.all(
          res.rows.map(async (course) => {
            const totalEstimatedTime = await this.calculateTotalEstimatedTime(ctx, course._id);
            const completionPercentage = await this.calculateCompletionPercentage(ctx, course._id, user._id);

            return {
              ...course,
              totalEstimatedCallTimeInMinutes: totalEstimatedTime,
              userCompletionPercentage: completionPercentage
            };
          })
        );

        return { ...res, rows: enrichedRows };
      } catch (error) {
        this.logger.error('Error in list after hook:', error);
        return res;
      }
    }
  }
}
```

## Khuyến nghị

**Hiện tại đang sử dụng Phương án 2 (Hook after)** vì:

1. **Ít risk hơn**: Không làm thay đổi core functionality
2. **Dễ debug**: Lỗi trong hook không làm crash toàn bộ list action
3. **Maintainable**: Code đơn giản, dễ hiểu
4. **Backward compatible**: Hoạt động với tất cả existing features

## Cách test

```bash
# Test API
curl -X GET "http://localhost:3000/api/courses?page=1&limit=5" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json"
```

## Response mong đợi

```json
{
  "total": 10,
  "page": 1,
  "pageSize": 5,
  "totalPages": 2,
  "rows": [
    {
      "_id": "course_id",
      "name": "Course Name",
      "description": "Course Description",
      "totalEstimatedCallTimeInMinutes": 45,
      "userCompletionPercentage": 75,
      // ... other fields
    }
  ]
}
```
