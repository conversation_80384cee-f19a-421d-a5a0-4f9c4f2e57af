/**
 * Migration script để loại bỏ trường scenarioId khỏi tasks collection
 * và cập nhật dữ liệu hiện có
 */

const mongoose = require('mongoose');

async function removeScenarioIdFromTasks() {
  console.log('🔄 Bắt đầu migration: Loại bỏ scenarioId khỏi tasks...');

  try {
    const db = mongoose.connection.db;
    const tasksCollection = db.collection('roleplay_tasks');
    const scenariosCollection = db.collection('roleplay_aiscenarios');

    // 1. Đếm số lượng tasks hiện có
    const totalTasks = await tasksCollection.countDocuments();
    console.log(`📊 Tổng số tasks hiện có: ${totalTasks}`);

    // 2. L<PERSON>y tất cả tasks có scenarioId
    const tasksWithScenarioId = await tasksCollection.find({
      scenarioId: { $exists: true }
    }).toArray();
    
    console.log(`📊 Số tasks có scenarioId: ${tasksWithScenarioId.length}`);

    // 3. Cậ<PERSON> nhật scenarios để đảm bảo taskIds được lưu trữ đúng
    for (const task of tasksWithScenarioId) {
      if (task.scenarioId) {
        try {
          // Kiểm tra scenario có tồn tại không
          const scenario = await scenariosCollection.findOne({
            _id: task.scenarioId
          });

          if (scenario) {
            // Thêm taskId vào mảng taskIds của scenario nếu chưa có
            const currentTaskIds = scenario.taskIds || [];
            const taskIdStr = task._id.toString();
            
            if (!currentTaskIds.some(id => id.toString() === taskIdStr)) {
              await scenariosCollection.updateOne(
                { _id: task.scenarioId },
                { 
                  $addToSet: { taskIds: task._id }
                }
              );
              console.log(`✅ Đã thêm task ${task._id} vào scenario ${task.scenarioId}`);
            }
          } else {
            console.log(`⚠️  Scenario ${task.scenarioId} không tồn tại cho task ${task._id}`);
          }
        } catch (error) {
          console.error(`❌ Lỗi khi cập nhật scenario cho task ${task._id}:`, error);
        }
      }
    }

    // 4. Loại bỏ trường scenarioId khỏi tất cả tasks
    const removeResult = await tasksCollection.updateMany(
      {},
      { 
        $unset: { scenarioId: "" }
      }
    );

    console.log(`✅ Đã loại bỏ scenarioId khỏi ${removeResult.modifiedCount} tasks`);

    // 5. Loại bỏ index cũ nếu tồn tại
    try {
      await tasksCollection.dropIndex({ scenarioId: 1, orderInScenario: 1 });
      console.log('✅ Đã xóa index { scenarioId: 1, orderInScenario: 1 }');
    } catch (error) {
      console.log('ℹ️  Index { scenarioId: 1, orderInScenario: 1 } không tồn tại hoặc đã được xóa');
    }

    // 6. Tạo index mới
    try {
      await tasksCollection.createIndex({ orderInScenario: 1 });
      console.log('✅ Đã tạo index { orderInScenario: 1 }');
    } catch (error) {
      console.log('ℹ️  Index { orderInScenario: 1 } đã tồn tại');
    }

    // 7. Kiểm tra kết quả
    const tasksAfterMigration = await tasksCollection.countDocuments({
      scenarioId: { $exists: true }
    });

    console.log(`📊 Số tasks còn lại có scenarioId: ${tasksAfterMigration}`);

    if (tasksAfterMigration === 0) {
      console.log('🎉 Migration hoàn thành thành công!');
    } else {
      console.log('⚠️  Vẫn còn một số tasks có scenarioId');
    }

    return {
      success: true,
      totalTasks,
      tasksWithScenarioId: tasksWithScenarioId.length,
      tasksModified: removeResult.modifiedCount,
      tasksRemaining: tasksAfterMigration
    };

  } catch (error) {
    console.error('❌ Lỗi trong quá trình migration:', error);
    throw error;
  }
}

// Rollback function (nếu cần)
async function rollbackRemoveScenarioId() {
  console.log('🔄 Bắt đầu rollback: Khôi phục scenarioId cho tasks...');
  
  try {
    const db = mongoose.connection.db;
    const tasksCollection = db.collection('roleplay_tasks');
    const scenariosCollection = db.collection('roleplay_aiscenarios');

    // Lấy tất cả scenarios
    const scenarios = await scenariosCollection.find({
      taskIds: { $exists: true, $ne: [] }
    }).toArray();

    let restoredCount = 0;

    for (const scenario of scenarios) {
      if (scenario.taskIds && scenario.taskIds.length > 0) {
        // Cập nhật tất cả tasks trong scenario này
        const updateResult = await tasksCollection.updateMany(
          { _id: { $in: scenario.taskIds } },
          { $set: { scenarioId: scenario._id } }
        );
        
        restoredCount += updateResult.modifiedCount;
        console.log(`✅ Đã khôi phục scenarioId cho ${updateResult.modifiedCount} tasks trong scenario ${scenario._id}`);
      }
    }

    console.log(`🎉 Rollback hoàn thành! Đã khôi phục scenarioId cho ${restoredCount} tasks`);

    return {
      success: true,
      tasksRestored: restoredCount
    };

  } catch (error) {
    console.error('❌ Lỗi trong quá trình rollback:', error);
    throw error;
  }
}

module.exports = {
  removeScenarioIdFromTasks,
  rollbackRemoveScenarioId
};
