'use strict';

const FunctionsCommon = require('../../../mixins/functionsCommon.mixin');
const BaseService = require('../../../mixins/baseService.mixin');
const FileMixin = require('../../../mixins/file.mixin');
const Model = require('./courses.model');
const DbMongoose = require('../../../mixins/dbMongo.mixin');
const i18next = require('i18next');
const {before} = require('lodash');
const {MoleculerClientError} = require('moleculer').Errors;

module.exports = {
  name: 'courses',
  mixins: [DbMongoose(Model), FunctionsCommon, BaseService, FileMixin],

  settings: {
    entityValidator: {
      name: {type: 'string', min: 2, max: 255},
      description: {type: 'string', optional: true, max: 5000},
      introduction: {type: 'string', optional: true, max: 5000},
      referenceUrls: {type: 'array', optional: true, items: 'string'},
      referenceFiles: {type: 'array', optional: true, items: 'string'},
      simulationType: {type: 'enum', values: ['Sale', 'Service', 'HR', 'Education', 'Other'], optional: true},
    },
    populates: {
      references: 'references.get',
      organizationId: 'organizations.get',
      createdBy: 'users.get',
      updatedBy: 'users.get',
    },
    populateOptions: ['references', 'organizationId', 'createdBy', 'updatedBy'],
    fields: [
      '_id',
      'name',
      'description',
      'introduction',
      'thumbnailId',
      'references',
      'simulationType',
      'organizationId',
      'createdBy',
      'updatedBy',
      'createdAt',
      'updatedAt',
      'status',
      'isDeleted',
      'totalEstimatedCallTimeInMinutes', // Tổng thời gian ước tính từ scenarios
      'userCompletionPercentage', // % hoàn thành kịch bản của user
    ],
  },

  hooks: {
    after: {
      'create|update|delete|addReferenceFile|removeReferenceFile': function (ctx, res) {
        const courseId = res._id;
        const performedBy = ctx.meta.user?._id;
        const action = ctx.action.name;
        this.logger.info(`${action} course ${courseId} by user ${performedBy}`);
        return res;
      },

      // Hook after cho list action để thêm thông tin tổng thời gian và % hoàn thành
      list: async function (ctx, res) {
        const user = ctx.meta.user;

        // Nếu không có user hoặc không có courses, trả về kết quả gốc
        if (!user || !res || !res.rows || !Array.isArray(res.rows)) {
          return res;
        }

        try {
          // Thêm thông tin tổng thời gian và % hoàn thành cho từng course
          const enrichedRows = await Promise.all(
            res.rows.map(async (course) => {
              try {
                // Tính tổng thời gian ước tính từ tất cả scenarios
                const totalEstimatedTime = await this.calculateTotalEstimatedTime(ctx, course._id);

                // Tính % hoàn thành kịch bản của user
                const completionPercentage = await this.calculateCompletionPercentage(ctx, course._id, user._id);

                return {
                  ...course,
                  totalEstimatedCallTimeInMinutes: totalEstimatedTime,
                  userCompletionPercentage: completionPercentage
                };
              } catch (error) {
                this.logger.error(`Error enriching course ${course._id}:`, error);
                return {
                  ...course,
                  totalEstimatedCallTimeInMinutes: 0,
                  userCompletionPercentage: 0
                };
              }
            })
          );

          // Trả về kết quả với cấu trúc pagination gốc
          return {
            ...res,
            rows: enrichedRows
          };
        } catch (error) {
          this.logger.error('Error in list after hook:', error);
          return res; // Trả về kết quả gốc nếu có lỗi
        }
      },
    },
    before: {
      update: async function (ctx) {},
    },
  },

  dependencies: ['files', 'organizations', 'users', 'references'],

  actions: {
    updateCourseByAI: {
      rest: 'POST /:id/updateByAI',
      params: {
        id: {type: 'string'}, // Course ID
        userPrompt: {type: 'string', optional: true}, // User's specific instructions for AI
      },
      async handler(ctx) {
        const {id, userPrompt} = ctx.params;
        const user = ctx.meta.user;

        // 1. Get Course Details
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        // Permission Check (example - adapt as needed)
        const hasPermissionToUpdate =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            course.organizationId &&
            user.organizationId.toString() === course.organizationId.toString()) ||
          (course.createdBy && user._id.toString() === course.createdBy.toString());

        if (!hasPermissionToUpdate) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền cập nhật khóa học này bằng AI'),
            403,
          );
        }

        // 2. Gather all reference content
        let referencesContent = '';
        if (course.references && course.references.length > 0) {
          const referenceObjects = await ctx.call('references.find', {query: {_id: {$in: course.references}}});
          referencesContent = referenceObjects.map(ref => ref.content || ref.url || ref.name).join('\n---\n');
        }

        // 3. Create/Update AI Persona
        this.logger.info(`Updating AI Persona for course ${id} using AI...`);
        let aiPersona;
        try {
          aiPersona = await ctx.call('aipersonas.createAIPersonaFromCourseContext', {
            courseId: id,
            courseName: course.name,
            courseDescription: course.description,
            courseReferencesContent: referencesContent,
            userPrompt: userPrompt ? `Liên quan đến AI Persona: ${userPrompt}` : undefined,
          });
          this.logger.info(`AI Persona ${aiPersona._id} created/updated for course ${id}.`);
        } catch (error) {
          this.logger.error(`Failed to create/update AI Persona for course ${id}:`, error);
          // Decide if this is a fatal error or if we can proceed without a new persona
          throw new MoleculerClientError(
            'Lỗi khi AI tạo Persona: ' + error.message,
            error.code || 500,
            error.type || 'AI_PERSONA_ERROR',
          );
        }

        // 4. Create Tasks
        this.logger.info(`Generating tasks for course ${id} using AI...`);
        let generatedTasks;
        // Construct a comprehensive prompt for task generation
        let taskPrompt = `Tên khóa học: ${course.name}`;
        if (course.description) taskPrompt += `\nMô tả: ${course.description}`;
        if (referencesContent) taskPrompt += `\nNội dung tham khảo chính: ${referencesContent.substring(0, 1500)}...`; // Truncate for brevity
        if (aiPersona)
          taskPrompt += `\nAI Persona được tạo: ${aiPersona.name} (Vai trò: ${aiPersona.role}, Bối cảnh: ${aiPersona.personaBackground.substring(0, 200)}...)`;
        if (userPrompt) taskPrompt += `\nYêu cầu cụ thể từ người dùng cho tasks: ${userPrompt}`;

        try {
          const taskCreationResult = await ctx.call('tasks.createTasksFromPrompt', {
            courseId: id,
            prompt: taskPrompt,
            aiPersonaId: aiPersona ? aiPersona._id : undefined,
          });
          generatedTasks = taskCreationResult.tasks;
          this.logger.info(`${generatedTasks.length} tasks generated for course ${id}.`);
        } catch (error) {
          this.logger.error(`Failed to generate tasks for course ${id}:`, error);
          // Decide if this is fatal or can proceed
          throw new MoleculerClientError(
            'Lỗi khi AI tạo Tasks: ' + error.message,
            error.code || 500,
            error.type || 'AI_TASK_ERROR',
          );
        }

        // 5. Create or Update AI Scenario with new AI Persona and Tasks
        const taskIds = generatedTasks.map(t => t._id);

        // Check if there's already a scenario for this course
        let aiScenario;
        try {
          aiScenario = await ctx.call('aiscenarios.getFirstByCourse', {courseId: id});
          // Update existing scenario
          aiScenario = await ctx.call('aiscenarios.update', {
            id: aiScenario._id,
            aiPersonaId: aiPersona._id,
            taskIds: taskIds,
            updatedBy: user._id,
          });
        } catch (error) {
          // Create new scenario if none exists
          aiScenario = await ctx.call('aiscenarios.create', {
            courseId: id,
            aiPersonaId: aiPersona._id,
            taskIds: taskIds,
            name: `${course.name} - Scenario`,
            description: `AI scenario for ${course.name}`,
            organizationId: course.organizationId,
            createdBy: user._id,
            updatedBy: user._id,
          });
        }

        // Update course timestamp
        const updatedCourse = await this.adapter.updateById(id, {
          $set: {
            updatedBy: user._id,
            updatedAt: new Date(),
          },
        });

        this.broker.emit('courses.updatedByAI', {course: updatedCourse, user, aiPersona, generatedTasks, aiScenario});
        return this.transformDocuments(ctx, {populate: this.settings.populateOptions}, updatedCourse);
      },
    },

    // Tạo khóa học mới
    createCourse: {
      rest: 'POST /',
      params: {
        name: {type: 'string', min: 2, max: 255},
        description: {type: 'string', optional: true, max: 5000},
        introduction: {type: 'string', optional: true, max: 5000},
        referenceUrls: {type: 'array', optional: true, items: 'string'},
        referenceFiles: {type: 'array', optional: true, items: 'string'},
        simulationType: {type: 'enum', values: ['Sale', 'Service', 'HR', 'Education', 'Other'], optional: true},
        organizationId: {type: 'string', optional: true},
      },
      async handler(ctx) {
        const {name, description, introduction, references, simulationType, organizationId} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Kiểm tra quyền tạo khóa học (system admin hoặc org admin)
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin && (!organizationId || user.organizationId.toString() === organizationId.toString()));

        if (!hasPermission) {
          throw new MoleculerClientError(i18next.t('error.permission_denied', 'Bạn không có quyền tạo khóa học'), 403);
        }

        const courseData = {
          name,
          description,
          introduction,
          references: references || [],
          simulationType,
          organizationId: organizationId || user.organizationId,
          createdBy: user._id,
          updatedBy: user._id,
          status: 'draft',
        };

        const course = await this.adapter.insert(courseData);
        this.broker.emit('courses.created', {course, user});
        return this.transformDocuments(ctx, {}, course);
      },
    },

    // Xóa khóa học (xóa mềm)
    deleteCourse: {
      rest: 'DELETE /:id',
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        const {id} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        // Kiểm tra quyền xóa khóa học
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            course.organizationId &&
            user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền xóa khóa học này'),
            403,
          );
        }

        const updated = await this.adapter.updateById(id, {
          $set: {
            isDeleted: true,
            deletedAt: new Date(),
            updatedBy: user._id,
          },
        });

        this.broker.emit('courses.deleted', {courseId: id, user});
        return {success: true, id};
      },
    },

    // Lấy chi tiết khóa học
    getCourse: {
      rest: 'GET /:id',
      params: {
        id: {type: 'string'},
        withScenarios: {type: 'boolean', optional: true, default: false},
      },
      async handler(ctx) {
        const {id, withScenarios} = ctx.params;
        const user = ctx.meta.user;

        // Lấy thông tin khóa học
        const course = await this.adapter.findOne({_id: id, isDeleted: {$ne: true}});
        if (!course) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        // Transform course data
        const data = await this.transformDocuments(ctx, {populate: this.settings.populateOptions}, course);

        // Tùy chọn lấy kèm AI scenarios
        if (withScenarios) {
          const scenarios = await ctx.call('aiscenarios.getByCourse', {courseId: id});
          data.scenarios = scenarios;
        }

        return data;
      },
    },

    // Lấy chi tiết khóa học với scenarios (alias cho getCourse với withScenarios=true)
    getCourseDetails: {
      rest: 'GET /:id/details',
      params: {
        id: {type: 'string'},
      },
      async handler(ctx) {
        return ctx.call('courses.getCourse', {
          id: ctx.params.id,
          withScenarios: true,
        });
      },
    },

    // Lấy danh sách nhiệm vụ của khóa học từ AI scenarios
    getTasksOfCourse: {
      rest: 'GET /:id/tasks',
      params: {
        id: {type: 'string'},
        scenarioId: {type: 'string', optional: true}, // Optional - nếu có thì lấy từ scenario cụ thể
      },
      async handler(ctx) {
        const {id, scenarioId} = ctx.params;

        // Lấy thông tin khóa học
        const course = await this.adapter.findOne({_id: id, isDeleted: {$ne: true}});
        if (!course) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        if (scenarioId) {
          // Case 1: Lấy tasks từ scenario cụ thể
          return await this.getTasksFromSpecificScenario(ctx, id, scenarioId);
        } else {
          // Case 2: Lấy tasks từ tất cả scenarios của course
          return await this.getTasksFromAllScenarios(ctx, id);
        }
      },
    },

    // Thêm tài liệu tham khảo vào khóa học
    addReferenceToCourse: {
      rest: 'POST /:id/references/:referenceId',
      params: {
        id: {type: 'string'},
        referenceId: {type: 'string'},
      },
      async handler(ctx) {
        const {id, referenceId} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        // Kiểm tra quyền cập nhật khóa học
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            course.organizationId &&
            user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền cập nhật khóa học này'),
            403,
          );
        }

        // Kiểm tra tài liệu tham khảo tồn tại
        const reference = await ctx.call('references.get', {id: referenceId}).catch(() => null);

        if (!reference) {
          throw new MoleculerClientError(
            i18next.t('error.reference_not_found', 'Không tìm thấy tài liệu tham khảo'),
            404,
          );
        }

        // Thêm tài liệu vào danh sách tham khảo
        if (!course.references) {
          course.references = [];
        }
        if (!course.references.includes(referenceId)) {
          course.references.push(referenceId);
        }

        // Cập nhật khóa học
        const updated = await this.adapter.updateById(id, {
          $set: {
            references: course.references,
            updatedBy: user._id,
            updatedAt: new Date(),
          },
        });

        return {success: true, reference, course: updated};
      },
    },

    // Xóa tài liệu tham khảo khỏi khóa học
    removeReferenceFromCourse: {
      rest: 'DELETE /:id/references/:referenceId',
      params: {
        id: {type: 'string'},
        referenceId: {type: 'string'},
      },
      async handler(ctx) {
        const {id, referenceId} = ctx.params;
        const user = ctx.meta.user;

        // Kiểm tra quyền truy cập
        if (!user) {
          throw new MoleculerClientError(i18next.t('error.unauthorized', 'Bạn chưa đăng nhập'), 401);
        }

        // Lấy thông tin khóa học để kiểm tra quyền
        const course = await this.adapter.findById(id);
        if (!course || course.isDeleted) {
          throw new MoleculerClientError(i18next.t('error.course_not_found', 'Không tìm thấy khóa học'), 404);
        }

        // Kiểm tra quyền cập nhật khóa học
        const hasPermission =
          user.isSystemAdmin ||
          (user.isOrgAdmin &&
            course.organizationId &&
            user.organizationId.toString() === course.organizationId.toString());

        if (!hasPermission) {
          throw new MoleculerClientError(
            i18next.t('error.permission_denied', 'Bạn không có quyền cập nhật khóa học này'),
            403,
          );
        }

        // Xóa tài liệu khỏi danh sách tham khảo
        if (course.references && course.references.includes(referenceId)) {
          course.references = course.references.filter(id => id.toString() !== referenceId);

          // Cập nhật khóa học
          await this.adapter.updateById(id, {
            $set: {
              references: course.references,
              updatedBy: user._id,
              updatedAt: new Date(),
            },
          });
        }

        return {success: true, id, referenceId};
      },
    },
  },

  events: {
    'courses.created': {
      async handler(payload) {
        this.logger.info(`New course created: ${payload.course.name}`);
      },
    },

    'courses.updated': {
      async handler(payload) {
        this.logger.info(`Course updated: ${payload.course._id}`);
      },
    },

    'courses.deleted': {
      async handler(payload) {
        this.logger.info(`Course deleted: ${payload.courseId}`);
      },
    },

    'references.created': {
      async handler(payload) {
        // Không cần xử lý gì đặc biệt khi tạo mới reference
        // Vì reference sẽ được thêm vào course thông qua action addReferenceToCourse
        this.logger.info(`New reference created: ${payload.reference._id}`);
      },
    },

    'references.updated': {
      async handler(payload) {
        // Khi reference được cập nhật, không cần thay đổi gì trong course
        this.logger.info(`Reference updated: ${payload.reference._id}`);
      },
    },

    'references.deleted': {
      async handler(payload) {
        if (payload.reference) {
          const referenceId = payload.reference._id || payload.referenceId;

          // Tìm tất cả các khóa học có chứa reference này
          const courses = await this.adapter.find({
            query: {
              references: referenceId,
              isDeleted: {$ne: true},
            },
          });

          // Xóa reference khỏi tất cả các khóa học
          for (const course of courses) {
            course.references = course.references.filter(id => id.toString() !== referenceId.toString());

            await this.adapter.updateById(course._id, {
              $set: {
                references: course.references,
                updatedAt: new Date(),
              },
            });

            this.logger.info(`Removed reference ${referenceId} from course ${course._id}`);
          }
        }
      },
    },

    'tasks.created': {
      async handler(payload) {
        if (payload.task && payload.task.courseId) {
          // Thêm task ID vào mảng taskIds của course
          const courseId = payload.task.courseId;
          const taskId = payload.task._id;
          const course = await this.adapter.findById(courseId);

          if (course && !course.isDeleted) {
            if (!course.taskIds) {
              course.taskIds = [];
            }
            if (!course.taskIds.includes(taskId)) {
              course.taskIds.push(taskId);
              await this.adapter.updateById(courseId, {
                $set: {taskIds: course.taskIds},
              });
              this.logger.info(`Added task ${taskId} to course ${courseId}`);
            }
          }
        }
      },
    },

    // Event handler 'tasks.deleted' đã được xử lý trong aiscenarios service
  },

  methods: {
    /**
     * Lấy tasks từ scenario cụ thể
     */
    async getTasksFromSpecificScenario(ctx, courseId, scenarioId) {
      try {
        // Lấy scenario cụ thể
        const scenario = await ctx.call('aiscenarios.get', {id: scenarioId});
        if (!scenario || scenario.courseId.toString() !== courseId) {
          throw new MoleculerClientError(i18next.t('error.scenario_not_found', 'Không tìm thấy kịch bản'), 404);
        }

        // Nếu scenario không có tasks
        if (!scenario.taskIds || scenario.taskIds.length === 0) {
          return {
            tasks: [],
            scenarios: [scenario],
            totalTasks: 0,
            message: 'Scenario không có nhiệm vụ nào',
          };
        }

        // Lấy danh sách tasks
        const tasks = await ctx.call('tasks.find', {
          query: {
            _id: {$in: scenario.taskIds},
            isDeleted: {$ne: true},
          },
          sort: 'orderInScenario',
        });

        // Thêm thông tin scenario vào mỗi task
        const tasksWithScenario = (tasks || []).map(task => ({
          ...task,
          scenario: {
            _id: scenario._id,
            name: scenario.name,
            description: scenario.description,
            aiPersonaId: scenario.aiPersonaId,
          },
        }));

        return {
          tasks: tasksWithScenario,
          scenarios: [scenario],
          totalTasks: tasksWithScenario.length,
        };
      } catch (error) {
        if (error.code === 404) {
          throw error;
        }
        this.logger.error(`Error getting tasks from scenario ${scenarioId}:`, error);
        throw new MoleculerClientError('Lỗi khi lấy nhiệm vụ từ kịch bản', 500);
      }
    },

    /**
     * Lấy tasks từ tất cả scenarios của course
     */
    async getTasksFromAllScenarios(ctx, courseId) {
      try {
        // Lấy tất cả scenarios của course
        const scenarios = await ctx.call('aiscenarios.getByCourse', {courseId});
        if (!scenarios || scenarios.length === 0) {
          return {
            tasks: [],
            scenarios: [],
            totalTasks: 0,
            message: 'Khóa học chưa có kịch bản nào',
          };
        }

        // Lấy tất cả taskIds từ các scenarios
        const allTaskIds = [];
        const scenarioMap = new Map(); // Map để lưu thông tin scenario cho mỗi task

        scenarios.forEach(scenario => {
          if (scenario.taskIds && scenario.taskIds.length > 0) {
            scenario.taskIds.forEach(taskId => {
              allTaskIds.push(taskId._id);
              scenarioMap.set(taskId._id.toString(), {
                _id: scenario._id,
                name: scenario.name,
                description: scenario.description,
                aiPersonaId: scenario.aiPersonaId,
              });
            });
          }
        });
        if (allTaskIds.length === 0) {
          return {
            tasks: [],
            scenarios: scenarios,
            totalTasks: 0,
            message: 'Tất cả kịch bản đều chưa có nhiệm vụ',
          };
        }

        // Lấy tất cả tasks
        const tasks = await ctx.call('tasks.find', {
          query: {
            _id: {$in: allTaskIds},
            isDeleted: {$ne: true},
          },
          sort: 'orderInScenario',
        });
        // Thêm thông tin scenario vào mỗi task
        const tasksWithScenario = (tasks || []).map(task => ({
          ...task,
          scenario: scenarioMap.get(task._id.toString()) || null,
        }));

        // Sắp xếp tasks theo scenario và orderInScenario
        tasksWithScenario.sort((a, b) => {
          // Sắp xếp theo scenario trước
          if (a.scenario && b.scenario && a.scenario._id !== b.scenario._id) {
            return a.scenario._id.localeCompare(b.scenario._id);
          }
          // Sau đó sắp xếp theo orderInScenario
          return (a.orderInScenario || 0) - (b.orderInScenario || 0);
        });

        return {
          tasks: tasksWithScenario,
          scenarios: scenarios,
          totalTasks: tasksWithScenario.length,
        };
      } catch (error) {
        this.logger.error(`Error getting tasks from all scenarios of course ${courseId}:`, error);
        throw new MoleculerClientError('Lỗi khi lấy nhiệm vụ từ các kịch bản', 500);
      }
    },

    /**
     * Kiểm tra quyền của user đối với khóa học
     *
     * @param {Object} user - Thông tin user
     * @param {Object} course - Thông tin khóa học
     * @param {String} permission - Loại quyền ('read', 'write', 'delete')
     * @returns {Boolean} - Có quyền hay không
     */
    hasPermission(user, course, permission = 'read') {
      if (!user) return false;

      // System admin có mọi quyền
      if (user.isSystemAdmin) return true;

      // Organization admin có quyền với khóa học trong tổ chức của mình
      if (
        user.isOrgAdmin &&
        course.organizationId &&
        user.organizationId &&
        user.organizationId.toString() === course.organizationId.toString()
      ) {
        return true;
      }

      // Người dùng thường chỉ có quyền đọc
      if (permission === 'read') {
        // Khóa học thuộc tổ chức của user
        if (
          course.organizationId &&
          user.organizationId &&
          user.organizationId.toString() === course.organizationId.toString()
        ) {
          return true;
        }
      }

      return false;
    },

    /**
     * Tính tổng thời gian ước tính từ tất cả AI scenarios của khóa học
     *
     * @param {Object} ctx - Moleculer context
     * @param {String} courseId - ID của khóa học
     * @returns {Number} - Tổng thời gian ước tính (phút)
     */
    async calculateTotalEstimatedTime(ctx, courseId) {
      try {
        // Lấy tất cả scenarios của course
        const scenarios = await ctx.call('aiscenarios.getByCourse', { courseId });

        if (!scenarios || scenarios.length === 0) {
          return 0;
        }

        // Tính tổng estimatedCallTimeInMinutes
        const totalTime = scenarios.reduce((total, scenario) => {
          return total + (scenario.estimatedCallTimeInMinutes || 0);
        }, 0);

        return totalTime;
      } catch (error) {
        this.logger.error(`Error calculating total estimated time for course ${courseId}:`, error);
        return 0;
      }
    },

    /**
     * Tính % hoàn thành kịch bản của người dùng trong khóa học
     *
     * @param {Object} ctx - Moleculer context
     * @param {String} courseId - ID của khóa học
     * @param {String} userId - ID của người dùng
     * @returns {Number} - % hoàn thành (0-100)
     */
    async calculateCompletionPercentage(ctx, courseId, userId) {
      try {
        // Lấy tất cả scenarios của course
        const scenarios = await ctx.call('aiscenarios.getByCourse', { courseId });

        if (!scenarios || scenarios.length === 0) {
          return 0;
        }

        // Lấy tất cả sessions đã completed của user cho course này
        const completedSessions = await ctx.call('roleplaysessions.find', {
          query: {
            studentId: userId,
            courseId: courseId,
            status: 'completed',
            isDeleted: false
          }
        });

        if (!completedSessions || completedSessions.length === 0) {
          return 0;
        }

        // Tạo Set các aiScenarioId đã hoàn thành
        const completedScenarioIds = new Set(
          completedSessions
            .filter(session => session.aiScenarioId)
            .map(session => session.aiScenarioId.toString())
        );

        // Tính % hoàn thành
        const totalScenarios = scenarios.length;
        const completedScenarios = scenarios.filter(scenario =>
          completedScenarioIds.has(scenario._id.toString())
        ).length;

        const percentage = totalScenarios > 0 ? Math.round((completedScenarios / totalScenarios) * 100) : 0;

        return percentage;
      } catch (error) {
        this.logger.error(`Error calculating completion percentage for course ${courseId}, user ${userId}:`, error);
        return 0;
      }
    },
  },
};
