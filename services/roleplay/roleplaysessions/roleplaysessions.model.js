const mongoose = require("mongoose");
const {Schema} = require("mongoose");
const {ROLEPLAY_SESSIONS, FILE, USER, ROLEPLAY_COURSES, ROLEPLAY_TASKS, ROLEPLAY_AIPERSONAS, ROLEPLAY_ANALYSIS} = require("../../../constants/dbCollections");

const transcriptSchema = new Schema({
  role: {
    type: String,
    enum: ['student', 'ai'],
    required: true
  },
  content: {
    type: String,
    required: true
  },
  timestamp: {
    type: Date,
    default: Date.now
  },
  audioId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: FILE
  },
  duration: { // Thời lượng của lượt nói này (tính bằng giây)
    type: Number
  },
  speakSpeed: { // Tốc độ nói (words/second)
    type: Number
  }
});

const schema = new Schema(
  {
    studentId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true,
      index: true
    },
    courseId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_COURSES,
      required: true
    },
    personaId: { // AI Persona được sử dụng cho phiên này
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_AIPERSONAS,
      required: true // Giả định mỗi session phải có persona
    },
    taskIds: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_TASKS
    }],
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed', 'analyzed'],
      default: 'pending'
    },
    startedAt: { type: Date },
    endTime: { type: Date },
    duration: { type: Number }, // Thời gian tương tác tính bằng giây
    recordingId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: FILE
    },
    analysisId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: ROLEPLAY_ANALYSIS
    },
    transcripts: [transcriptSchema],
    isCompleted: { type: Boolean, default: false },
    isDeleted: { type: Boolean, default: false },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER,
      required: true
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: USER
    },
  },
  {
    timestamps: {
      createdAt: "createdAt",
      updatedAt: "updatedAt",
    },
    versionKey: false,
  }
);

module.exports = mongoose.model(ROLEPLAY_SESSIONS, schema, ROLEPLAY_SESSIONS);
