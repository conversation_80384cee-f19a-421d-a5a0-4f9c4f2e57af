// Test script để kiểm tra API courses list với thông tin mới
// Chạy: node test-courses-list.js

const axios = require('axios');

// C<PERSON>u hình
const BASE_URL = process.env.API_BASE_URL || 'http://localhost:3000';
const API_ENDPOINT = '/api/courses';

// Thay thế bằng JWT token thực tế
const JWT_TOKEN = process.env.JWT_TOKEN || 'YOUR_JWT_TOKEN_HERE';

// Test function
async function testCoursesListAPI() {
  try {
    console.log('🚀 Testing Courses List API with new fields...\n');

    // Gọi API courses list
    const response = await axios.get(`${BASE_URL}${API_ENDPOINT}`, {
      headers: {
        'Authorization': `Bearer ${JWT_TOKEN}`,
        'Content-Type': 'application/json'
      },
      params: {
        limit: 5, // Giới hạn 5 courses để test
        page: 1
      }
    });

    console.log('✅ API Response Status:', response.status);
    console.log('📊 Response Data Structure:');
    console.log('- Total:', response.data.total);
    console.log('- Page:', response.data.page);
    console.log('- PageSize:', response.data.pageSize);
    console.log('- TotalPages:', response.data.totalPages);
    console.log('- Rows Count:', response.data.rows?.length || 0);

    if (response.data.rows && response.data.rows.length > 0) {
      console.log('\n📋 Sample Course Data:');
      const sampleCourse = response.data.rows[0];

      console.log('Course ID:', sampleCourse._id);
      console.log('Course Name:', sampleCourse.name);
      console.log('🕒 Total Estimated Time:', sampleCourse.totalEstimatedCallTimeInMinutes, 'minutes');
      console.log('📈 User Completion Percentage:', sampleCourse.userCompletionPercentage, '%');

      // Kiểm tra các trường mới có tồn tại không
      const hasNewFields =
        sampleCourse.hasOwnProperty('totalEstimatedCallTimeInMinutes') &&
        sampleCourse.hasOwnProperty('userCompletionPercentage');

      if (hasNewFields) {
        console.log('✅ New fields are present in the response!');
      } else {
        console.log('❌ New fields are missing from the response!');
      }

      // Hiển thị tất cả courses với thông tin mới
      console.log('\n📚 All Courses Summary:');
      response.data.rows.forEach((course, index) => {
        console.log(`${index + 1}. ${course.name}`);
        console.log(`   - Estimated Time: ${course.totalEstimatedCallTimeInMinutes || 0} minutes`);
        console.log(`   - Completion: ${course.userCompletionPercentage || 0}%`);
        console.log('');
      });
    } else {
      console.log('⚠️ No courses found in the response');
    }

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    if (error.response) {
      console.error('Response Status:', error.response.status);
      console.error('Response Data:', error.response.data);
    }
  }
}

// Chạy test
testCoursesListAPI();

// Export để có thể sử dụng trong test khác
module.exports = { testCoursesListAPI };
